import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import {
  completeModalInteraction,
  waitForModalType,
  fillModalInput,
  clickModalAction,
  waitForModalToClose
} from '../helpers/modal-helpers';
import {
  openGhostTab,
  clickSyncButton,
  waitForGhostTabStatus,
  waitForSyncToComplete,
  getGhostTabSyncStatus
} from '../helpers/ghost-tab-helpers';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { test, expect } from 'vitest';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Excerpt Editing", () => {
  const context = setupE2ETestHooks();

  test("should open excerpt edit modal when clicking excerpt edit button", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "Synced");

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);
    expect(modal.modalType).toBe('excerpt-edit');

    // Verify modal elements are present
    const textarea = await context.page.waitForSelector('[data-input="excerpt"]', { timeout: 5000 });
    expect(textarea).toBeTruthy();

    const saveButton = await context.page.waitForSelector('[data-action="save"]', { timeout: 5000 });
    expect(saveButton).toBeTruthy();

    const cancelButton = await context.page.waitForSelector('[data-action="cancel"]', { timeout: 5000 });
    expect(cancelButton).toBeTruthy();

    // Close the modal
    await clickModalAction(context.page, 'cancel');
    await waitForModalToClose(context.page);
  });

  test("should save excerpt when editing through modal", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt Save";
    const testExcerpt = "This is a test excerpt that should be saved to the Ghost post. It contains enough text to verify the functionality works properly.";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "Synced");

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);

    // Fill in the excerpt
    await fillModalInput(context.page, 'excerpt', testExcerpt);

    // Verify character count is displayed
    const charCount = await context.page.textContent('.excerpt-char-count');
    expect(charCount).toContain(`${testExcerpt.length}/300`);

    // Save the excerpt
    await clickModalAction(context.page, 'save');
    await waitForModalToClose(context.page);

    // Wait for the save operation to complete
    await expectNotice(context, "Excerpt updated successfully");

    // Verify the excerpt is displayed in the UI
    const excerptText = await context.page.textContent('.ghost-excerpt-text');
    expect(excerptText).toContain(testExcerpt);
  });

  test("should cancel excerpt editing without saving changes", async () => {
    // Create and sync a test post first
    const testTitle = "Test Post for Excerpt Cancel";

    await executeCommand(context, 'Ghost Sync: Create new post');
    await completeModalInteraction(
      context.page,
      'create-post',
      { 'post-title': testTitle },
      'submit'
    );

    await expectNotice(context, "Created new post");
    await openGhostTab(context);
    await waitForGhostTabStatus(context.page, 'new-post');

    // Sync the post to Ghost
    await clickSyncButton(context.page);
    await waitForSyncToComplete(context.page);
    await expectNotice(context, "Synced");

    // Wait for the excerpt section to appear
    await context.page.waitForSelector('.ghost-excerpt-section', { timeout: 10000 });

    // Get the original excerpt text
    const originalExcerpt = await context.page.textContent('.ghost-excerpt-text');

    // Click the excerpt edit button
    const excerptEditButton = await context.page.waitForSelector('.ghost-excerpt-edit-btn', { timeout: 5000 });
    await excerptEditButton.click();

    // Wait for the excerpt edit modal to appear
    const modal = await waitForModalType(context.page, 'excerpt-edit');
    expect(modal.found).toBe(true);

    // Fill in some text
    await fillModalInput(context.page, 'excerpt', 'This text should not be saved');

    // Cancel the edit
    await clickModalAction(context.page, 'cancel');
    await waitForModalToClose(context.page);

    // Verify the excerpt text hasn't changed
    const currentExcerpt = await context.page.textContent('.ghost-excerpt-text');
    expect(currentExcerpt).toBe(originalExcerpt);
  });


});
