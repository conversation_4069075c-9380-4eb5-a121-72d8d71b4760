import { Page } from 'playwright';
import { executeCommand, expectNotice, type SharedTestContext } from './shared-context';

/**
 * Opens the Ghost tab in the right sidebar
 */
export async function openGhostTab(context: SharedTestContext): Promise<void> {
  await executeCommand(context, 'Ghost Sync: Open Ghost Sync Status');

  // Wait for the Ghost tab to appear in the right sidebar
  await context.page.waitForSelector('.ghost-sync-status-view', { timeout: 10000 });
}

/**
 * Clicks the sync button in the Ghost tab
 */
export async function clickSyncButton(page: Page): Promise<void> {
  // Look for the sync button - it could be "Sync" or "Sync to Ghost"
  const syncButton = page.locator('.ghost-sync-btn').filter({
    hasText: /^(Sync|Sync to Ghost)$/
  });

  await syncButton.waitFor({ timeout: 5000 });
  await syncButton.click();
}

/**
 * Clicks the publish button in the Ghost tab
 */
export async function clickPublishButton(page: Page): Promise<void> {
  const publishButton = page.locator('.ghost-sync-btn').filter({
    hasText: 'Publish'
  });

  await publishButton.waitFor({ timeout: 5000 });
  await publishButton.click();
}

/**
 * Clicks the browse posts button in the Ghost tab
 */
export async function clickBrowsePostsButton(page: Page): Promise<void> {
  const browseButton = page.locator('.ghost-sync-btn').filter({
    hasText: 'Browse Posts'
  });

  await browseButton.waitFor({ timeout: 5000 });
  await browseButton.click();
}

/**
 * Waits for the Ghost tab to show a specific status
 */
export async function waitForGhostTabStatus(page: Page, status: 'new-post' | 'synced' | 'no-file' | 'not-article'): Promise<void> {
  const selectors = {
    'new-post': '.ghost-sync-new-post',
    'synced': '.ghost-properties',
    'no-file': '.ghost-sync-no-file',
    'not-article': '.ghost-sync-not-article'
  };

  const selector = selectors[status];
  if (!selector) {
    throw new Error(`Unknown status: ${status}`);
  }

  await page.waitForSelector(selector, { timeout: 10000 });
}

/**
 * Gets the current sync status information from the Ghost tab
 */
export async function getGhostTabSyncStatus(page: Page): Promise<{
  isNewPost: boolean;
  title?: string;
  slug?: string;
  status?: string;
}> {
  return await page.evaluate(() => {
    const ghostTab = document.querySelector('.ghost-sync-status-view');
    if (!ghostTab) {
      throw new Error('Ghost tab not found');
    }

    // Check if it's a new post
    const newPostElement = ghostTab.querySelector('.ghost-sync-new-post');
    if (newPostElement) {
      const titleElement = ghostTab.querySelector('.ghost-sync-new-post-details div:first-child');
      const slugElement = ghostTab.querySelector('.ghost-sync-new-post-details div:nth-child(2)');

      return {
        isNewPost: true,
        title: titleElement?.textContent?.replace('Title: ', '') || undefined,
        slug: slugElement?.textContent?.replace('Slug: ', '') || undefined
      };
    }

    // Check for synced post status
    const statusContainer = ghostTab.querySelector('.ghost-sync-status-container');
    if (statusContainer) {
      // Extract status information from the synced post view
      const statusElement = ghostTab.querySelector('.ghost-sync-status-item:has(.ghost-sync-status-label:contains("Status"))');

      return {
        isNewPost: false,
        status: statusElement?.textContent?.trim() || undefined
      };
    }

    return { isNewPost: false };
  });
}

/**
 * Checks if the Ghost tab is visible and accessible
 */
export async function isGhostTabVisible(page: Page): Promise<boolean> {
  try {
    await page.waitForSelector('.ghost-sync-status-view', { timeout: 1000 });
    return true;
  } catch {
    return false;
  }
}

/**
 * Waits for sync operation to complete by watching for notice or status change
 * Enhanced with better error handling and debugging
 */
export async function waitForSyncToComplete(page: Page, timeout: number = 15000): Promise<void> {
  try {
    // Wait for either a success notice or error notice
    await page.waitForFunction(
      () => {
        const notices = document.querySelectorAll('.notice');
        return Array.from(notices).some(notice =>
          notice.textContent?.includes('Synced') ||
          notice.textContent?.includes('Created') ||
          notice.textContent?.includes('Error') ||
          notice.textContent?.includes('Updated')
        );
      },
      {},
      { timeout }
    );
  } catch (error) {
    // Get current notices for debugging
    const currentNotices = await page.evaluate(() => {
      const noticeElements = document.querySelectorAll('.notice');
      return Array.from(noticeElements).map(el => el.textContent || '');
    });

    throw new Error(
      `Sync operation did not complete within ${timeout}ms. ` +
      `Current notices: ${JSON.stringify(currentNotices)}. ` +
      `Original error: ${error.message}`
    );
  }
}

/**
 * Simple sync helper that opens Ghost tab, clicks sync, and waits for completion
 * Enhanced to use expectNotice for better reliability
 */
export async function syncPost(context: SharedTestContext): Promise<void> {
  await openGhostTab(context);
  await clickSyncButton(context.page);

  // Use expectNotice instead of waitForSyncToComplete for better reliability
  await expectNotice(context, "Synced", 15000);
}
