import { mount, unmount } from 'svelte';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import GhostSyncView from '../../src/components/GhostSyncView.svelte';
import {
  createMockPlugin,
  createMockFile,
  createMockSyncStatus,
  createSyncedSyncStatus,
  createMockContainer,
  cleanupContainer,
  waitForNextTick
} from '../test-utils';

describe('GhostSyncView Component', () => {
  let container: HTMLElement;
  let component: any;
  let mockPlugin: any;

  beforeEach(() => {
    container = createMockContainer();
    mockPlugin = createMockPlugin();
  });

  afterEach(() => {
    if (component) {
      unmount(component);
      component = null;
    }
    cleanupContainer(container);
  });

  describe('Rendering', () => {
    it('should render without crashing', () => {
      expect(() => {
        component = mount(GhostSyncView, {
          target: container,
          props: {
            currentFile: null,
            syncStatus: createMockSyncStatus(),
            plugin: mockPlugin
          }
        });
      }).not.toThrow();
    });

    it('should show "No file selected" when currentFile is null', () => {
      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: null,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toContain('No file selected');
    });

    it('should show directory warning when file is not in articles directory', () => {
      const fileOutsideArticles = createMockFile('other/test-post.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileOutsideArticles,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toMatch(/File must be in.*directory/);
    });

    it('should show sync interface when file is in articles directory', () => {
      const fileInArticles = createMockFile('articles/test-post.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createSyncedSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toContain('Sync');
      expect(container.textContent).toContain('Browse Posts');
    });

    it('should render property displays', () => {
      const fileInArticles = createMockFile('articles/test-post.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createSyncedSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toContain('Title');
      expect(container.textContent).toContain('Slug');
      expect(container.textContent).toContain('Status');
      expect(container.textContent).toContain('Tags');
    });
  });

  describe('Event Handling', () => {
    it('should emit smartSync event when sync button is clicked', async () => {
      const fileInArticles = createMockFile('articles/test-post.md');
      let smartSyncCalled = false;

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createSyncedSyncStatus(),
          plugin: mockPlugin
        }
      });

      component.$on('smartSync', () => {
        smartSyncCalled = true;
      });

      const syncButtons = container.querySelectorAll('button');
      const syncButton = Array.from(syncButtons).find(btn =>
        btn.textContent?.includes('Sync') && !btn.textContent?.includes('Browse')
      );

      expect(syncButton).toBeTruthy();
      syncButton?.click();
      await waitForNextTick();

      expect(smartSyncCalled).toBe(true);
    });

    it('should emit browsePosts event when browse posts button is clicked', async () => {
      const fileInArticles = createMockFile('articles/test-post.md');
      let browsePostsCalled = false;

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createSyncedSyncStatus(),
          plugin: mockPlugin
        }
      });

      component.$on('browsePosts', () => {
        browsePostsCalled = true;
      });

      const buttons = container.querySelectorAll('button');
      const browseButton = Array.from(buttons).find(btn =>
        btn.textContent?.includes('Browse Posts')
      );

      expect(browseButton).toBeTruthy();
      browseButton?.click();
      await waitForNextTick();

      expect(browsePostsCalled).toBe(true);
    });

    it('should emit smartSync event when sync button is clicked', async () => {
      const fileInArticles = createMockFile('articles/test-post.md');
      let smartSyncEmitted = false;

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createSyncedSyncStatus(),
          plugin: mockPlugin
        }
      });

      component.$on('smartSync', () => {
        smartSyncEmitted = true;
      });

      const buttons = container.querySelectorAll('button');
      const syncButton = Array.from(buttons).find(btn =>
        btn.textContent?.includes('Sync') && !btn.textContent?.includes('Browse')
      );

      expect(syncButton).toBeTruthy();
      syncButton?.click();
      await waitForNextTick();

      expect(smartSyncEmitted).toBe(true);
    });

    it('should disable publish button when post is published and email was sent', () => {
      const fileInArticles = createMockFile('articles/test-post.md');
      const publishedWithEmailStatus = {
        ...createSyncedSyncStatus(),
        ghostPost: {
          ...createSyncedSyncStatus().ghostPost,
          status: 'published',
          email: { id: 'email-123' } // Email was sent
        }
      };

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: publishedWithEmailStatus,
          plugin: mockPlugin
        }
      });

      const buttons = container.querySelectorAll('button');
      const publishButton = Array.from(buttons).find(btn =>
        btn.textContent?.includes('Publish')
      );
      expect(publishButton).toBeTruthy();
      expect(publishButton?.disabled).toBe(true);
      expect(publishButton?.classList.contains('disabled')).toBe(true);
    });

    it('should enable publish button when post is published but email was not sent', () => {
      const fileInArticles = createMockFile('articles/test-post.md');
      const publishedWithoutEmailStatus = {
        ...createSyncedSyncStatus(),
        ghostPost: {
          ...createSyncedSyncStatus().ghostPost,
          status: 'published',
          email: null as any // No email sent
        }
      };

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: publishedWithoutEmailStatus,
          plugin: mockPlugin
        }
      });

      const buttons = container.querySelectorAll('button');
      const publishButton = Array.from(buttons).find(btn =>
        btn.textContent?.includes('Publish')
      );
      expect(publishButton).toBeTruthy();
      expect(publishButton?.disabled).toBe(false);
      expect(publishButton?.classList.contains('disabled')).toBe(false);
    });
  });

  describe('Props Handling', () => {
    it('should handle null plugin gracefully', () => {
      expect(() => {
        component = mount(GhostSyncView, {
          target: container,
          props: {
            currentFile: null,
            syncStatus: createMockSyncStatus(),
            plugin: null
          }
        });
      }).not.toThrow();
    });

    it('should handle undefined syncStatus gracefully', () => {
      expect(() => {
        component = mount(GhostSyncView, {
          target: container,
          props: {
            currentFile: null,
            syncStatus: undefined as any,
            plugin: mockPlugin
          }
        });
      }).not.toThrow();
    });

    it('should update when props change', async () => {
      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: null,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toContain('No file selected');

      // Update props
      const fileInArticles = createMockFile('articles/test-post.md');
      component.$set({
        currentFile: fileInArticles
      });

      await waitForNextTick();

      expect(container.textContent).toContain('Sync');
    });
  });

  describe('File Path Logic', () => {
    it('should correctly identify files in articles directory', () => {
      const fileInArticles = createMockFile('articles/test-post.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileInArticles,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      // Should show sync interface, not directory warning
      expect(container.textContent).not.toMatch(/File must be in.*directory/);
      expect(container.textContent).toContain('Sync');
    });

    it('should correctly identify files outside articles directory', () => {
      const fileOutside = createMockFile('notes/other-file.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: fileOutside,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).toMatch(/File must be in.*directory/);
    });

    it('should handle nested files in articles directory', () => {
      const nestedFile = createMockFile('articles/subfolder/nested-post.md');

      component = mount(GhostSyncView, {
        target: container,
        props: {
          currentFile: nestedFile,
          syncStatus: createMockSyncStatus(),
          plugin: mockPlugin
        }
      });

      expect(container.textContent).not.toMatch(/File must be in.*directory/);
      expect(container.textContent).toContain('Sync');
    });
  });
});
